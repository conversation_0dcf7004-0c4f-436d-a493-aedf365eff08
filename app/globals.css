/* styles/globals.css */

/* import the entire Tailwind engine (preflight + components + utilities) */
@import "tailwindcss";

/* then your custom glass/shadow utilities */
.glass-filter {
  filter: url(#glass-distortion);
}
.transition-custom {
  transition: all .4s cubic-bezier(.175,.885,.32,2.2);
}
.beautiful-shadow {
  box-shadow:
    0 2.8px 2.2px rgba(0,0,0,0.034),
    0 6.7px 5.3px rgba(0,0,0,0.048),
    0 12.5px 10px rgba(0,0,0,0.06),
    0 22.3px 17.9px rgba(0,0,0,0.072),
    0 41.8px 33.4px rgba(0,0,0,0.086),
    0 100px 80px rgba(0,0,0,0.12);
}/* your shared “glass” & shadow utilities */
.glass-filter {
  filter: url(#glass-distortion);
}
.transition-custom {
  transition: all .4s cubic-bezier(.175,.885,.32,2.2);
}
.beautiful-shadow {
  box-shadow:
    0 2.8px 2.2px rgba(0,0,0,.034),
    0 6.7px 5.3px rgba(0,0,0,.048),
    0 12.5px 10px rgba(0,0,0,.06),
    0 22.3px 17.9px rgba(0,0,0,.072),
    0 41.8px 33.4px rgba(0,0,0,.086),
    0 100px 80px rgba(0,0,0,.12);
}