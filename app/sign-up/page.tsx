'use client'

import { useRouter } from 'next/navigation'
import Card from '@/components/ui/Card'
import OnboardingLayout from '@/components/ui/OnboardingLayout'
import { Input } from '@/components/ui/input'

export default function SignUpPage() {
  const router = useRouter()

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    router.push('/verification')
  }

  return (
    <OnboardingLayout>
      <Card maxW="max-w-2xl">
        <div className="mb-6 text-center">
          <h1 className="text-2xl font-bold">Create your account</h1>
          <p className="text-sm text-muted-foreground">Step 1 of 3 – Sign up</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid gap-2">
            <label htmlFor="email" className="text-sm font-medium">Email</label>
            <Input id="email" type="email" required placeholder="<EMAIL>" />
          </div>

          <div className="grid gap-2">
            <label htmlFor="phone" className="text-sm font-medium">Phone Number</label>
            <Input id="phone" type="tel" required placeholder="+251..." />
          </div>

          <div className="grid gap-2">
            <label htmlFor="password" className="text-sm font-medium">Password</label>
            <Input id="password" type="password" required placeholder="••••••••" />
          </div>

          <button
            type="submit"
            className="w-full py-3 text-base font-semibold bg-gradient-to-r from-white/30 to-white/20 rounded-xl backdrop-blur-sm glass-filter hover:bg-white/30 transition"
          >
            Continue to Step 2 →
          </button>
        </form>
      </Card>
    </OnboardingLayout>
  )
}