'use client'

import { useRouter } from 'next/navigation'

export default function SignUpPage() {
  const router = useRouter()

  return (
    <div className="relative flex items-center justify-center min-h-screen font-light bg-[url(https://images.unsplash.com/photo-1635151227785-429f420c6b9d?w=2160&q=80)] bg-cover">
      {/* Dark overlay */}
      <div className="absolute inset-0 bg-black/60" />

      <div className="relative z-10 flex flex-col overflow-hidden cursor-default beautiful-shadow transition-custom max-w-2xl w-full font-semibold text-white rounded-3xl mx-4">
        {/* glass layers */}
        <div className="absolute inset-0 z-0 backdrop-blur-md glass-filter isolate" />
        <div className="absolute inset-0 z-10 bg-white bg-opacity-15" />
        <div
          className="absolute inset-0 z-20 overflow-hidden shadow-inner"
          style={{
            boxShadow:
              'inset 2px 2px 1px 0 rgba(255,255,255,.5), inset -1px -1px 1px 1px rgba(255,255,255,.5)',
            borderRadius: '24px',
          }}
        />

        {/* Header */}
        <div className="z-30 flex flex-col items-center justify-center text-center bg-black/10 pt-8 px-8">
          <div className="mb-4">
            <div className="relative inline-flex items-center justify-center w-24 h-24 rounded-2xl mb-3 overflow-hidden">
              <div className="absolute inset-0 z-0 backdrop-blur-sm glass-filter" />
              <div className="absolute inset-0 z-10 bg-gradient-to-br from-white/30 to-white/10" />
              <div
                className="absolute inset-0 z-20"
                style={{
                  boxShadow:
                    'inset 3px 3px 2px 0 rgba(255,255,255,.6), inset -2px -2px 2px 2px rgba(255,255,255,.4)',
                  borderRadius: '16px',
                }}
              />
              {/* replace with /public/logo.png or keep remote */}
              <img src="/logo.png" alt="ቀለሜ Logo" className="z-20 w-full h-full object-contain" />
            </div>
            <h1 className="text-5xl font-normal tracking-tighter mb-2">Create your ቀለሜ account</h1>
            <p className="text-sm font-light text-white/80">
              Let’s get you set up with your new account in just a few simple steps.
            </p>
          </div>

          {/* Progress */}
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-2">
              <div className="step-indicator active w-8 h-8 rounded-lg flex items-center justify-center text-xs font-semibold backdrop-blur-sm">
                1
              </div>
              <span className="text-xs font-medium text-white/90 hidden sm:block">Sign up</span>
            </div>
            <div className="w-6 h-px bg-white/30" />
            <div className="flex items-center gap-2">
              <div className="step-indicator w-8 h-8 rounded-lg flex items-center justify-center text-xs font-semibold backdrop-blur-sm bg-white/10">
                2
              </div>
              <span className="text-xs font-medium text-white/60 hidden sm:block">Verification</span>
            </div>
            <div className="w-6 h-px bg-white/30" />
            <div className="flex items-center gap-2">
              <div className="step-indicator w-8 h-8 rounded-lg flex items-center justify-center text-xs font-semibold backdrop-blur-sm bg-white/10">
                3
              </div>
              <span className="text-xs font-medium text-white/60 hidden sm:block">
                Personalization
              </span>
            </div>
          </div>
        </div>

        {/* Form */}
        <div className="z-30 flex flex-col p-8 overflow-y-auto">
          <div className="mb-6">
            <h2 className="text-2xl font-medium mb-2">Tell us about yourself</h2>
            <p className="text-sm font-normal text-white/70">
              Step 1 of 3 • This helps us personalize your experience
            </p>
          </div>

          <form
            onSubmit={(e) => {
              e.preventDefault()
              router.push('/verification')
            }}
            className="flex-1 mb-6 space-y-4"
          >
            {/* First & Last Name */}
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label htmlFor="firstName" className="block text-sm font-medium mb-2">
                  First name
                </label>
                <div className="relative overflow-hidden rounded-xl">
                  <div className="absolute inset-0 z-0 backdrop-blur-sm glass-filter" />
                  <div className="absolute inset-0 z-10 bg-white bg-opacity-10" />
                  <div
                    className="absolute inset-0 z-20"
                    style={{
                      boxShadow:
                        'inset 1px 1px 1px 0 rgba(255,255,255,.3), inset -1px -1px 1px 1px rgba(255,255,255,.1)',
                      borderRadius: '12px',
                    }}
                  />
                  <input
                    id="firstName"
                    type="text"
                    placeholder="adel"
                    required
                    className="z-30 relative w-full bg-transparent px-4 py-3 text-sm placeholder-gray-300 text-white focus:outline-none"
                  />
                </div>
              </div>
              <div>
                <label htmlFor="lastName" className="block text-sm font-medium mb-2">
                  Last name
                </label>
                <div className="relative overflow-hidden rounded-xl">
                  <div className="absolute inset-0 z-0 backdrop-blur-sm glass-filter" />
                  <div className="absolute inset-0 z-10 bg-white bg-opacity-10" />
                  <div
                    className="absolute inset-0 z-20"
                    style={{
                      boxShadow:
                        'inset 1px 1px 1px 0 rgba(255,255,255,.3), inset -1px -1px 1px 1px rgba(255,255,255,.1)',
                      borderRadius: '12px',
                    }}
                  />
                  <input
                    id="lastName"
                    type="text"
                    placeholder="abdu"
                    required
                    className="z-30 relative w-full bg-transparent px-4 py-3 text-sm placeholder-gray-300 text-white focus:outline-none"
                  />
                </div>
              </div>
            </div>

            {/* Email or Phone */}
            <div>
              <label htmlFor="contact" className="block text-sm font-medium mb-2">
                Email or phone number
              </label>
              <div className="relative overflow-hidden rounded-xl">
                <div className="absolute inset-0 z-0 backdrop-blur-sm glass-filter" />
                <div className="absolute inset-0 z-10 bg-white bg-opacity-10" />
                <div
                  className="absolute inset-0 z-20"
                  style={{
                    boxShadow:
                      'inset 1px 1px 1px 0 rgba(255,255,255,.3), inset -1px -1px 1px 1px rgba(255,255,255,.1)',
                    borderRadius: '12px',
                  }}
                />
                <svg
                  className="pointer-events-none absolute left-3 top-1/2 -translate-y-1/2 w-4 h-4 text-white/60"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  viewBox="0 0 24 24"
                >
                  <path d="M22 12h-4m-4 0H2m0 0c0 2.21 3.582 4 8 4s8-1.79 8-4m-16 0c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                </svg>
                <input
                  id="contact"
                  type="text"
                  inputMode="email"
                  autoComplete="email tel"
                  placeholder="<EMAIL> or +251 9 123 45678"
                  required
                  className="z-30 relative w-full bg-transparent pl-10 pr-4 py-3 text-sm placeholder-gray-300 text-white focus:outline-none"
                />
              </div>
            </div>

            {/* Password */}
            <div>
              <label htmlFor="password" className="block text-sm font-medium mb-2">
                Create password
              </label>
              <div className="relative overflow-hidden rounded-xl">
                <div className="absolute inset-0 z-0 backdrop-blur-sm glass-filter" />
                <div className="absolute inset-0 z-10 bg-white bg-opacity-10" />
                <div
                  className="absolute inset-0 z-20"
                  style={{
                    boxShadow:
                      'inset 1px 1px 1px 0 rgba(255,255,255,.3), inset -1px -1px 1px 1px rgba(255,255,255,.1)',
                    borderRadius: '12px',
                  }}
                />
                <input
                  id="password"
                  type="password"
                  placeholder="Minimum 8 characters"
                  required
                  className="z-30 relative w-full bg-transparent px-4 py-3 text-sm placeholder-gray-300 text-white focus:outline-none"
                />
              </div>
            </div>

            {/* Terms */}
            <div className="flex gap-3 pt-2 items-center">
              <input
                type="checkbox"
                required
                className="w-4 h-4 bg-opacity-20 border-opacity-30 focus:ring-0 focus:ring-offset-0 bg-white border-white rounded"
              />
              <p className="text-sm font-normal leading-relaxed text-white/70">
                I agree to the{' '}
                <a href="#" className="underline font-medium hover:opacity-80">
                  Terms of Service
                </a>{' '}
                and{' '}
                <a href="#" className="underline font-medium hover:opacity-80">
                  Privacy Policy
                </a>
                .
              </p>
            </div>

            {/* Continue */}
            <div className="relative overflow-hidden rounded-xl transition-custom hover:shadow-lg">
              <div className="absolute inset-0 z-0 backdrop-blur-sm glass-filter" />
              <div className="absolute inset-0 z-10 bg-gradient-to-r from-white/30 to-white/20" />
              <div
                className="absolute inset-0 z-20"
                style={{
                  boxShadow:
                    'inset 2px 2px 1px 0 rgba(255,255,255,.5), inset -1px -1px 1px 1px rgba(255,255,255,.3)',
                  borderRadius: '12px',
                }}
              />
              <button
                type="submit"
                className="z-30 relative w-full py-4 flex items-center justify-center gap-2 text-base font-semibold text-white bg-transparent"
              >
                Continue to Step 2
                <svg
                  width="16"
                  height="16"
                  fill="none"
                  stroke="currentColor"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  viewBox="0 0 24 24"
                >
                  <path d="M13 7l5 5-5 5M6 12h12" />
                </svg>
              </button>
            </div>
          </form>

          {/* Footer */}
          <div className="text-center mt-auto">
            <p className="text-sm font-normal text-white/70 mb-3">
              Already have an account?
              <a href="#" className="font-semibold hover:opacity-80">
                {' '}
                Sign in here
              </a>
            </p>
            <div className="flex items-center justify-center gap-4 text-xs font-normal text-white/50">
              <a href="#" className="hover:text-white/70">
                Help Center
              </a>
              <span>•</span>
              <a href="#" className="hover:text-white/70">
                Contact Support
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}