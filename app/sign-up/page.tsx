// app/sign-up/page.tsx
import Card from '/Users/<USER>/keleme-ui/components/ui/Card'
import OnboardingLayout from '/Users/<USER>/keleme-ui/components/ui/OnboardingLayout'
import { useRouter } from 'next/navigation'

export default function SignUpPage() {
  const router = useRouter()
  return (
    <OnboardingLayout>
      <Card maxW="max-w-4xl">
        {/* logo, header, progress bar */}
        <form onSubmit={e => { e.preventDefault(); router.push('/verification') }} className="space-y-6">
          {/* your inputs… */}
          <button type="submit" className="w-full py-4 text-base font-semibold bg-gradient-to-r from-white/30 to-white/20 rounded-xl backdrop-blur-sm glass-filter">
            Continue to Step 2 →
          </button>
        </form>
      </Card>
    </OnboardingLayout>
  )
}