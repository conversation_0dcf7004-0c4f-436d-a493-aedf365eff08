// components/OnboardingLayout.tsx
import React from 'react'

/** 
 * Wraps any step in the full-screen background + dark overlay + centering.
 * Usage: <OnboardingLayout><Card>…</Card></OnboardingLayout>
 */
export default function OnboardingLayout({ children }: { children: React.ReactNode }) {
  return (
    <div
      className="min-h-screen flex items-center justify-center bg-[url('/bg.jpg')] bg-cover relative"
      style={{ backgroundImage: "url('https://images.unsplash.com/photo-1635151227785-429f420c6b9d?w=2160&q=80')" }}
    >
      {/* dark overlay */}
      <div className="absolute inset-0 bg-black/60" />
      {children}
    </div>
  )
}