// components/Card.tsx
import React from 'react'
import clsx from 'clsx'

/** 
 * A glassy, shadowed container that shrink-wraps its content up to a max width.
 * Usage: <Card maxW="max-w-4xl">…</Card>
 */
export default function Card({
  children,
  maxW = 'max-w-2xl',
}: {
  children: React.ReactNode
  maxW?: string
}) {
  return (
    <div
      className={clsx(
        'relative z-10 flex flex-col overflow-hidden cursor-default',
        'w-fit mx-4 p-8 rounded-3xl text-white font-medium',
        'bg-white/15 backdrop-blur-md glass-filter',
        'beautiful-shadow transition-custom',
        maxW
      )}
    >
      {children}
    </div>
  )
}